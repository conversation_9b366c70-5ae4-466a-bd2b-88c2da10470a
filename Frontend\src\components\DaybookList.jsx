import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  BookOpen,
  Plus,
  Edit,
  Eye,
  Calendar,
  User,
  DollarSign,
  Lock,
  Unlock,
  Search,
  Filter,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";
import Badge from "./shared/Badge";
import LoadingSkeleton from "./shared/LoadingSkeleton";
import { useToast } from "./shared/Toast";
import { daybooksAPI } from "../services/api.js";

const DaybookList = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const [daybooks, setDaybooks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [currentUser, setCurrentUser] = useState(null);

  // Sample data - replace with API call later
  const sampleDaybooks = [
    {
      id: 1,
      page_number: 15,
      date: "2024-01-15",
      created_by: {
        id: 1,
        name: "احمد علي خان",
        username: "ahmad",
      },
      status: "open",
      total_jamah: 125000,
      total_naam: 98000,
      balance: 27000,
      transaction_count: 8,
      created_at: "2024-01-15T10:30:00Z",
    },
    {
      id: 2,
      page_number: 16,
      date: "2024-01-16",
      created_by: {
        id: 2,
        name: "فاطمه احمد",
        username: "fatima",
      },
      status: "closed",
      total_jamah: 85000,
      total_naam: 75000,
      balance: 10000,
      transaction_count: 5,
      created_at: "2024-01-16T09:15:00Z",
    },
    {
      id: 3,
      page_number: 17,
      date: "2024-01-17",
      created_by: {
        id: 1,
        name: "احمد علي خان",
        username: "ahmad",
      },
      status: "open",
      total_jamah: 200000,
      total_naam: 180000,
      balance: 20000,
      transaction_count: 12,
      created_at: "2024-01-17T11:45:00Z",
    },
    {
      id: 4,
      page_number: 18,
      date: "2024-01-18",
      created_by: {
        id: 3,
        name: "محمد حسن",
        username: "hassan",
      },
      status: "open",
      total_jamah: 150000,
      total_naam: 140000,
      balance: 10000,
      transaction_count: 7,
      created_at: "2024-01-18T08:20:00Z",
    },
  ];

  useEffect(() => {
    loadDaybooks();
    loadCurrentUser();
  }, []);

  const loadCurrentUser = () => {
    // Get current user from localStorage
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    setCurrentUser(user);
  };

  const loadDaybooks = async () => {
    setLoading(true);
    try {
      const response = await daybooksAPI.getAll();
      console.log("📋 DaybookList: Loaded daybooks:", response);

      // Handle different response structures
      let daybooksData = [];
      if (Array.isArray(response)) {
        daybooksData = response;
      } else if (response.results && Array.isArray(response.results)) {
        daybooksData = response.results;
      } else if (response.data && Array.isArray(response.data)) {
        daybooksData = response.data;
      } else {
        console.warn("Unexpected response structure:", response);
        daybooksData = [];
      }

      setDaybooks(daybooksData);
    } catch (error) {
      console.error("❌ DaybookList: Error loading daybooks:", error);

      let errorMessage = "د ورځني کتابونو د لوډولو کې ستونزه";
      if (error.response?.data?.detail) {
        errorMessage += `: ${error.response.data.detail}`;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });

      // Fallback to sample data for development
      console.log("📋 DaybookList: Using sample data as fallback");
      setDaybooks(sampleDaybooks);
    } finally {
      setLoading(false);
    }
  };

  const canEditDaybook = (daybook) => {
    if (!currentUser) return false;

    // Super admin and admin can edit all daybooks
    if (currentUser.role === "super_admin" || currentUser.role === "admin") {
      return true;
    }

    // Creator can edit their own daybook
    return (
      daybook.created_by?.username === currentUser.username || daybook.can_edit
    );
  };

  const handleCreateNew = async () => {
    try {
      setLoading(true);
      const today = new Date().toISOString().split("T")[0];

      // Get the next available page number for today
      let nextPageNumber = 1;
      try {
        nextPageNumber = await daybooksAPI.getNextPageNumber(today);
        console.log("📄 Next page number:", nextPageNumber);
      } catch (error) {
        console.log("Could not fetch next page number, using page number 1");
      }

      const newDaybookData = {
        page_number: nextPageNumber,
        date: today,
        status: "open",
      };

      console.log("📝 Creating daybook with data:", newDaybookData);
      const response = await daybooksAPI.create(newDaybookData);
      console.log("✅ Daybook created:", response);

      toast.show({
        type: "success",
        message: "نوی ورځني کتاب جوړ شو",
      });

      // Navigate to the new daybook for editing
      navigate(`/daybook/${response.id}/edit`);
    } catch (error) {
      console.error("❌ Error creating daybook:", error);

      let errorMessage = "د نوي ورځني کتاب جوړولو کې ستونزه";
      if (error.response?.data?.non_field_errors) {
        errorMessage += `: ${error.response.data.non_field_errors.join(", ")}`;
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleView = (daybook) => {
    console.log("📖 DaybookList: Navigating to view daybook:", daybook.id);
    navigate(`/daybook/${daybook.id}/view`);
  };

  const handleEdit = (daybook) => {
    console.log("✏️ DaybookList: Attempting to edit daybook:", daybook.id);

    if (canEditDaybook(daybook)) {
      console.log(
        "✅ DaybookList: User has edit permission, navigating to edit mode"
      );
      navigate(`/daybook/${daybook.id}/edit`);
    } else {
      console.log("❌ DaybookList: User does not have edit permission");
      toast.show({
        type: "error",
        message: "تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ",
      });
    }
  };

  const filteredDaybooks = (daybooks || []).filter((daybook) => {
    if (!daybook) return false;

    const matchesSearch =
      daybook.page_number?.toString().includes(searchTerm) ||
      (daybook.created_by?.name || daybook.created_by_name || "").includes(
        searchTerm
      ) ||
      (daybook.date || "").includes(searchTerm);

    const matchesStatus =
      statusFilter === "all" || daybook.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    if (!dateString) return "نامعلوم";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("fa-AF");
    } catch (error) {
      return dateString; // Return original string if parsing fails
    }
  };

  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return "0 افغانۍ";
    try {
      return amount.toLocaleString() + " افغانۍ";
    } catch (error) {
      return amount + " افغانۍ";
    }
  };

  if (loading) {
    return (
      <Layout title='د ورځني کتابونو لیست'>
        <LoadingSkeleton />
      </Layout>
    );
  }

  return (
    <Layout title='د ورځني کتابونو لیست'>
      <div className='space-y-8'>
        {/* Header */}
        <div className='bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-8 border border-blue-100'>
          <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6'>
            <div>
              <h1 className='text-3xl font-bold text-gray-800 pashto-text mb-2'>
                د ورځني کتابونو لیست
              </h1>
              <p className='text-gray-600 pashto-text text-lg'>
                ټول ورځني کتابونه دلته لیست شوي دي
              </p>
            </div>
            <Button
              variant='primary'
              icon={Plus}
              onClick={handleCreateNew}
              loading={loading}
              disabled={loading}
              className='px-6 py-3 text-lg font-medium shadow-lg hover:shadow-xl transition-shadow duration-300'
            >
              نوی ورځني کتاب
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className='bg-white rounded-xl shadow-lg p-6 border border-gray-100'>
          <div className='flex flex-col sm:flex-row gap-6'>
            {/* Search */}
            <div className='flex-1'>
              <label className='block text-sm font-medium text-gray-700 mb-2 pashto-text'>
                پلټنه
              </label>
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5' />
                <input
                  type='text'
                  placeholder='د صفحې شمیره، جوړونکی یا نیټه پلټنه...'
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className='w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pashto-text text-sm shadow-sm'
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className='sm:w-56'>
              <label className='block text-sm font-medium text-gray-700 mb-2 pashto-text'>
                د حالت له مخې فلټر
              </label>
              <div className='relative'>
                <Filter className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5' />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className='w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pashto-text text-sm shadow-sm appearance-none bg-white'
                >
                  <option value='all'>ټول حالتونه</option>
                  <option value='open'>خلاص</option>
                  <option value='closed'>تړل شوی</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Daybooks Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 auto-rows-fr'>
          {filteredDaybooks.map((daybook) => (
            <div
              key={daybook.id}
              className='bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 overflow-hidden group flex flex-col h-full'
            >
              {/* Card Header */}
              <div className='bg-gradient-to-r from-blue-500 to-blue-600 p-4'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-3'>
                    <div className='bg-white bg-opacity-20 p-2 rounded-lg'>
                      <BookOpen className='h-6 w-6 text-white' />
                    </div>
                    <div>
                      <h3 className='font-bold text-xl text-white pashto-text'>
                        صفحه {daybook.page_number}
                      </h3>
                      <p className='text-blue-100 text-sm pashto-text'>
                        {formatDate(daybook.date)}
                      </p>
                    </div>
                  </div>
                  <div
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      daybook.status === "open"
                        ? "bg-green-100 text-green-800 border border-green-200"
                        : "bg-red-100 text-red-800 border border-red-200"
                    }`}
                  >
                    <span className='pashto-text'>
                      {daybook.status === "open" ? "خلاص" : "تړل شوی"}
                    </span>
                  </div>
                </div>
              </div>

              <div className='p-6 flex-1 flex flex-col'>
                {/* Info */}
                <div className='space-y-4 mb-6'>
                  <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                    <div className='flex items-center space-x-2'>
                      <User className='h-5 w-5 text-blue-600' />
                      <span className='text-sm font-medium text-gray-700 pashto-text'>
                        جوړونکی:
                      </span>
                    </div>
                    <span className='text-sm font-semibold text-gray-900 pashto-text'>
                      {daybook.created_by?.name ||
                        daybook.created_by_name ||
                        "نامعلوم"}
                    </span>
                  </div>

                  <div className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                    <div className='flex items-center space-x-2'>
                      <DollarSign className='h-5 w-5 text-blue-600' />
                      <span className='text-sm font-medium text-gray-700 pashto-text'>
                        میزان:
                      </span>
                    </div>
                    <span
                      className={`text-sm font-bold ${
                        daybook.balance >= 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {formatCurrency(daybook.balance)}
                    </span>
                  </div>
                </div>

                {/* Stats */}
                <div className='grid grid-cols-2 gap-4 mb-6'>
                  <div className='text-center p-4 bg-gradient-to-br from-green-50 to-green-100 rounded-lg border border-green-200'>
                    <div className='text-sm font-medium text-green-600 pashto-text mb-1'>
                      جمعه
                    </div>
                    <div className='font-bold text-green-700 text-lg'>
                      {formatCurrency(daybook.total_jamah)}
                    </div>
                  </div>
                  <div className='text-center p-4 bg-gradient-to-br from-red-50 to-red-100 rounded-lg border border-red-200'>
                    <div className='text-sm font-medium text-red-600 pashto-text mb-1'>
                      نام
                    </div>
                    <div className='font-bold text-red-700 text-lg'>
                      {formatCurrency(daybook.total_naam)}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className='flex space-x-3 mt-auto'>
                  <Button
                    variant='outline'
                    size='sm'
                    icon={Eye}
                    onClick={() => handleView(daybook)}
                    className='flex-1 py-2 px-4 text-sm font-medium border-2 hover:bg-blue-50 hover:border-blue-300 transition-colors duration-200'
                  >
                    کتل
                  </Button>

                  {canEditDaybook(daybook) ? (
                    <Button
                      variant='primary'
                      size='sm'
                      icon={Edit}
                      onClick={() => handleEdit(daybook)}
                      className='flex-1 py-2 px-4 text-sm font-medium bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-md hover:shadow-lg transition-all duration-200'
                    >
                      تصحیح
                    </Button>
                  ) : (
                    <Button
                      variant='outline'
                      size='sm'
                      icon={Lock}
                      disabled
                      className='flex-1 py-2 px-4 text-sm font-medium opacity-50 cursor-not-allowed'
                    >
                      بند
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredDaybooks.length === 0 && (
          <div className='text-center py-16'>
            <div className='bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-12 border border-gray-200'>
              <div className='bg-blue-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6'>
                <BookOpen className='h-10 w-10 text-blue-600' />
              </div>
              <h3 className='text-2xl font-bold text-gray-900 pashto-text mb-3'>
                هیڅ ورځني کتاب ونه موندل شو
              </h3>
              <p className='text-gray-600 pashto-text mb-8 text-lg max-w-md mx-auto'>
                د نوي ورځني کتاب جوړولو لپاره لاندې تڼۍ کلیک کړئ
              </p>
              <Button
                variant='primary'
                icon={Plus}
                onClick={handleCreateNew}
                loading={loading}
                disabled={loading}
                className='px-8 py-3 text-lg font-medium shadow-lg hover:shadow-xl transition-shadow duration-300'
              >
                نوی ورځني کتاب
              </Button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DaybookList;
