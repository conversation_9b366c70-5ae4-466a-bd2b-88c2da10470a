import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useLocation } from "react-router-dom";
import {
  Plus,
  Save,
  Lock,
  Unlock,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Check,
  X,
  AlertCircle,
  ArrowLeft,
  Eye,
  Edit,
  BookOpen,
  User,
} from "lucide-react";
import Layout from "./shared/Layout";
import Button from "./shared/Button";
import { useToast } from "./shared/Toast";
import {
  daybooksAPI,
  daybookTransactionsAPI,
  jamaTransactionsAPI,
  naamTransactionsAPI,
  customersAPI,
} from "../services/api.js";

const Daybook = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const [currentDaybook, setCurrentDaybook] = useState(null);
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("جمعه"); // Track active sheet
  const [jamaRows, setJamaRows] = useState([]);
  const [namRows, setNamRows] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [canEdit, setCanEdit] = useState(false);
  const [isViewMode, setIsViewMode] = useState(false);
  const [urlMode, setUrlMode] = useState("view"); // 'edit', 'view', or 'new'

  // Calculate effective amount including commission
  const calculateEffectiveAmount = (amount, commission) => {
    const baseAmount = parseFloat(amount) || 0;
    const commissionAmount = parseFloat(commission) || 0;
    return baseAmount + commissionAmount;
  };

  // Calculate totals whenever rows change
  useEffect(() => {
    if (currentDaybook) {
      const jamaTotal = jamaRows
        .filter((row) => row.amount && !isNaN(parseFloat(row.amount)))
        .reduce(
          (sum, row) =>
            sum + calculateEffectiveAmount(row.amount, row.commission),
          0
        );

      const namTotal = namRows
        .filter((row) => row.amount && !isNaN(parseFloat(row.amount)))
        .reduce(
          (sum, row) =>
            sum + calculateEffectiveAmount(row.amount, row.commission),
          0
        );

      const balance = jamaTotal - namTotal;

      setCurrentDaybook((prev) => ({
        ...prev,
        total_jamah: jamaTotal,
        total_naam: namTotal,
        balance: balance,
      }));
    }
  }, [jamaRows, namRows]);

  // د نمونه ډیټا
  const sampleDaybooks = {
    1: {
      id: 1,
      date: new Date().toLocaleDateString("fa-AF"),
      page_number: 15,
      status: "خلاص", // خلاص یا تړل شوی
      total_jamah: 125000,
      total_naam: 98000,
      balance: 27000,
      created_by: {
        id: 1,
        username: "admin",
        full_name: "د سیسټم مدیر",
      },
    },
    2: {
      id: 2,
      date: new Date().toLocaleDateString("fa-AF"),
      page_number: 16,
      status: "خلاص",
      total_jamah: 85000,
      total_naam: 75000,
      balance: 10000,
      created_by: {
        id: 2,
        username: "user1",
        full_name: "احمد علي خان",
      },
    },
  };

  const sampleTransactions = [
    {
      id: 1,
      index: 1,
      customer_name: "احمد علي خان",
      type: "جمعه",
      amount: 25000,
      commission: 500,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      balance_after: 25000,
    },
    {
      id: 2,
      index: 2,
      customer_name: "فاطمه احمد",
      type: "نام",
      amount: 15000,
      commission: -200,
      currency: "افغانۍ",
      description: "د پیسو ورکول",
      balance_after: 10000,
    },
    {
      id: 3,
      index: 3,
      customer_name: "محمد حسن",
      type: "جمعه",
      amount: 50000,
      commission: 1000,
      currency: "افغانۍ",
      description: "د پیسو اخیستل",
      balance_after: 60000,
    },
  ];

  const customers = [
    { value: "1", label: "احمد علي خان" },
    { value: "2", label: "فاطمه احمد" },
    { value: "3", label: "محمد حسن" },
    { value: "4", label: "عایشه خان" },
    { value: "5", label: "علي رضا" },
  ];

  const currencies = [
    { value: "افغانۍ", label: "افغانۍ (AFN)" },
    { value: "ډالر", label: "ډالر (USD)" },
    { value: "یورو", label: "یورو (EUR)" },
    { value: "پاکستاني روپۍ", label: "پاکستاني روپۍ (PKR)" },
  ];

  useEffect(() => {
    // Determine mode from URL
    const path = location.pathname;
    console.log("🔍 Daybook: Current path:", path);

    if (path.includes("/edit")) {
      setUrlMode("edit");
      console.log("✏️ Daybook: URL mode set to EDIT");
    } else if (path.includes("/view")) {
      setUrlMode("view");
      console.log("👁️ Daybook: URL mode set to VIEW");
    } else if (path.includes("/new")) {
      setUrlMode("new");
      console.log("🆕 Daybook: URL mode set to NEW");
    } else {
      setUrlMode("view"); // Default to view mode
      console.log("👁️ Daybook: URL mode set to VIEW (default)");
    }

    loadCurrentUser();
    loadDaybook();
  }, [id, location.pathname]);

  const loadCurrentUser = () => {
    // Get current user from localStorage
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    setCurrentUser(user);
  };

  const checkEditPermission = (daybook, user) => {
    if (!user || !daybook) return false;

    // Super admin and admin can edit all daybooks
    if (user.role === "super_admin" || user.role === "admin") {
      return true;
    }

    // Creator can edit their own daybook
    // Check both created_by.username and created_by.id for safety
    return (
      daybook.created_by?.username === user.username ||
      daybook.created_by?.id === user.id ||
      daybook.can_edit === true
    );
  };

  const loadDaybook = async () => {
    setLoading(true);
    console.log(
      "📚 Daybook: Loading daybook with ID:",
      id,
      "URL mode:",
      urlMode
    );

    try {
      if (id && id !== "new") {
        console.log("📖 Daybook: Loading existing daybook from API");
        // Load existing daybook
        const daybook = await daybooksAPI.getById(id);
        console.log("✅ Daybook: Loaded daybook data:", daybook);
        setCurrentDaybook(daybook);

        // Load transactions from separate tables
        console.log("📊 Daybook: Loading transactions for daybook ID:", id);

        let jamaResponse = [];
        let naamResponse = [];

        try {
          [jamaResponse, naamResponse] = await Promise.all([
            jamaTransactionsAPI.getByDaybook(id),
            naamTransactionsAPI.getByDaybook(id),
          ]);
        } catch (error) {
          console.warn(
            "⚠️ Daybook: Error loading separate transactions, trying fallback:",
            error
          );

          // Fallback to old transaction API if separate APIs don't exist
          try {
            const oldTransactions = await daybookTransactionsAPI.getByDaybook(
              id
            );
            console.log(
              "📊 Daybook: Using fallback transactions:",
              oldTransactions
            );

            // Separate old transactions by type
            const allOldTransactions = Array.isArray(oldTransactions)
              ? oldTransactions
              : [];
            jamaResponse = allOldTransactions.filter(
              (t) => t.transaction_type === "jamah"
            );
            naamResponse = allOldTransactions.filter(
              (t) => t.transaction_type === "naam"
            );
          } catch (fallbackError) {
            console.error(
              "❌ Daybook: Fallback transaction loading failed:",
              fallbackError
            );
            jamaResponse = [];
            naamResponse = [];
          }
        }

        console.log("📊 Daybook: Raw jama response:", jamaResponse);
        console.log("📊 Daybook: Raw naam response:", naamResponse);

        // Ensure we have arrays - handle different response structures
        let jamaTransactions = [];
        let naamTransactions = [];

        // Handle jama transactions
        if (Array.isArray(jamaResponse)) {
          jamaTransactions = jamaResponse;
        } else if (jamaResponse && Array.isArray(jamaResponse.results)) {
          jamaTransactions = jamaResponse.results;
        } else if (jamaResponse && Array.isArray(jamaResponse.data)) {
          jamaTransactions = jamaResponse.data;
        } else {
          console.warn(
            "⚠️ Daybook: Unexpected jama response structure:",
            jamaResponse
          );
          jamaTransactions = [];
        }

        // Handle naam transactions
        if (Array.isArray(naamResponse)) {
          naamTransactions = naamResponse;
        } else if (naamResponse && Array.isArray(naamResponse.results)) {
          naamTransactions = naamResponse.results;
        } else if (naamResponse && Array.isArray(naamResponse.data)) {
          naamTransactions = naamResponse.data;
        } else {
          console.warn(
            "⚠️ Daybook: Unexpected naam response structure:",
            naamResponse
          );
          naamTransactions = [];
        }

        console.log("✅ Daybook: Processed transactions:", {
          jamaCount: jamaTransactions.length,
          naamCount: naamTransactions.length,
        });

        // Combine for backward compatibility
        const allTransactions = [
          ...jamaTransactions.map((t) => ({ ...t, transaction_type: "jamah" })),
          ...naamTransactions.map((t) => ({ ...t, transaction_type: "naam" })),
        ];
        setTransactions(allTransactions);

        // Map jama transactions with error handling
        try {
          setJamaRows(
            jamaTransactions.map((t) => ({
              id: t.id,
              customer_name: t.customer_name || "",
              description: t.description || "",
              amount: t.amount || 0,
              commission: t.commission || 0,
              currency: t.currency || "AFN",
              balance: t.balance_after || 0,
            }))
          );
        } catch (error) {
          console.error("❌ Daybook: Error mapping jama transactions:", error);
          setJamaRows([]);
        }

        // Map naam transactions with error handling
        try {
          setNamRows(
            naamTransactions.map((t) => ({
              id: t.id,
              customer_name: t.customer_name || "",
              description: t.description || "",
              amount: t.amount || 0,
              commission: t.commission || 0,
              currency: t.currency || "AFN",
              balance: t.balance_after || 0,
            }))
          );
        } catch (error) {
          console.error("❌ Daybook: Error mapping naam transactions:", error);
          setNamRows([]);
        }
      } else {
        // New daybook - initialize empty with next available page number
        const today = new Date().toISOString().split("T")[0];

        // Get the next available page number for today
        let nextPageNumber = 1;
        try {
          nextPageNumber = await daybooksAPI.getNextPageNumber(today);
        } catch (error) {
          console.log("Could not fetch next page number, using page number 1");
        }

        setCurrentDaybook({
          id: null,
          date: today,
          page_number: nextPageNumber,
          status: "open",
          total_jamah: 0,
          total_naam: 0,
          balance: 0,
          created_by: JSON.parse(localStorage.getItem("user") || "{}"),
        });

        // Initialize with empty rows
        const initialRow = {
          id: null,
          customer_name: "",
          description: "",
          amount: "",
          commission: "",
          currency: "AFN",
          balance: "",
        };

        setJamaRows([initialRow]);
        setNamRows([{ ...initialRow }]);
      }

      // Check permissions - can only edit if user has permission AND daybook is open
      const user = JSON.parse(localStorage.getItem("user") || "{}");
      const hasUserPermission = currentDaybook
        ? checkEditPermission(currentDaybook, user)
        : true;
      const isDaybookOpen = !currentDaybook || currentDaybook.status === "open";
      const canUserEdit = hasUserPermission && isDaybookOpen;

      // Determine if user can edit based on URL mode and permissions
      const shouldAllowEdit =
        (urlMode === "edit" || urlMode === "new") && canUserEdit;
      setCanEdit(shouldAllowEdit);

      // Set view mode based on URL mode and permissions
      const shouldBeViewMode = urlMode === "view" || !canUserEdit;
      setIsViewMode(shouldBeViewMode);

      console.log("🔐 Daybook: Permission check:", {
        urlMode,
        hasUserPermission,
        isDaybookOpen,
        canUserEdit,
        shouldAllowEdit,
        shouldBeViewMode,
      });
    } catch (error) {
      console.error("❌ Daybook: Error loading daybook:", error);

      let errorMessage = "د ورځني کتاب د لوډولو کې ستونزه";
      if (error.response?.status === 404) {
        errorMessage = "ورځني کتاب ونه موندل شو";
        // Redirect to daybook list if daybook not found
        setTimeout(() => {
          navigate("/daybook-list");
        }, 2000);
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
      navigate("/daybook-list");
    } finally {
      setLoading(false);
    }
  };

  // Handle cell input changes
  const handleCellChange = (rowIndex, field, value, sheetType) => {
    // Validate input based on field type
    let validatedValue = value;

    if (field === "amount") {
      // Only allow positive numbers for amount
      const numValue = parseFloat(value);
      if (value !== "" && (isNaN(numValue) || numValue < 0)) {
        return; // Don't update if invalid
      }
    }

    if (field === "customer_name") {
      // Trim whitespace and limit length
      validatedValue = value.trim().substring(0, 100);
    }

    if (field === "description") {
      // Limit description length
      validatedValue = value.substring(0, 200);
    }

    if (sheetType === "جمعه") {
      setJamaRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: validatedValue };
        return newRows;
      });
    } else {
      setNamRows((prev) => {
        const newRows = [...prev];
        newRows[rowIndex] = { ...newRows[rowIndex], [field]: validatedValue };
        return newRows;
      });
    }
  };

  // Handle Enter key press to move to next row
  const handleKeyPress = (e, rowIndex, field, sheetType) => {
    if (e.key === "Enter") {
      e.preventDefault();

      // Always add new row when pressing Enter, regardless of current row content
      const currentRows = sheetType === "جمعه" ? jamaRows : namRows;

      // If this is the last row, add a new one
      if (rowIndex === currentRows.length - 1) {
        addSingleRow(sheetType);
      }

      // Focus next row's first input
      setTimeout(() => {
        const nextRowInput = document.querySelector(
          `[data-row="${rowIndex + 1}"][data-field="${
            sheetType === "جمعه" ? "jama" : "nam"
          }_customer"][data-sheet="${sheetType}"]`
        );
        if (nextRowInput) {
          nextRowInput.focus();
        }
      }, 100);
    }
  };

  // Add single row when needed
  const addSingleRow = (sheetType) => {
    const newRow = {
      id: (sheetType === "جمعه" ? jamaRows.length : namRows.length) + 1,
      customer_name: "",
      description: "",
      amount: "",
      commission: "",
      currency: "افغانۍ",
      balance: "",
    };

    if (sheetType === "جمعه") {
      setJamaRows((prev) => [...prev, newRow]);
    } else {
      setNamRows((prev) => [...prev, newRow]);
    }
  };

  // Delete row function
  const deleteRow = (rowIndex, sheetType) => {
    if (sheetType === "جمعه") {
      setJamaRows((prev) => prev.filter((_, index) => index !== rowIndex));
    } else {
      setNamRows((prev) => prev.filter((_, index) => index !== rowIndex));
    }
  };

  const toggleDaybookStatus = async () => {
    if (!currentDaybook?.id) {
      toast.show({
        type: "error",
        message: "لومړی ورځني کتاب ثبت کړئ",
      });
      return;
    }

    const newStatus = currentDaybook.status === "closed" ? "open" : "closed";
    const actionText = newStatus === "closed" ? "تړل" : "خلاصول";

    // Show confirmation dialog for locking
    if (newStatus === "closed") {
      const confirmed = window.confirm(
        "ایا تاسو ډاډه یاست چې غواړئ دا ورځني کتاب وتړئ؟ د تړلو وروسته به نشي تصحیح کیدای."
      );
      if (!confirmed) return;
    }

    try {
      setLoading(true);

      const updatedDaybook = await daybooksAPI.update(currentDaybook.id, {
        ...currentDaybook,
        status: newStatus,
      });

      setCurrentDaybook(updatedDaybook);
      setCanEdit(newStatus === "open" && updatedDaybook.can_edit);

      toast.show({
        type: "success",
        message:
          newStatus === "closed"
            ? "ورځني کتاب بریالیتوب سره تړل شو"
            : "ورځني کتاب بریالیتوب سره خلاص شو",
      });
    } catch (error) {
      console.error("Error updating daybook status:", error);

      let errorMessage = "د ورځني کتاب د حالت د بدلولو کې ستونزه";
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  const validateDaybook = () => {
    const errors = [];

    // Validate daybook basic info
    if (!currentDaybook?.date) {
      errors.push("نیټه اړینه ده");
    }
    if (!currentDaybook?.page_number || currentDaybook.page_number < 1) {
      errors.push("د پاڼې شمیره اړینه ده");
    }

    // Validate transactions
    const jamaTransactions = jamaRows.filter(
      (row) => row.customer_name?.trim() || row.amount
    );
    const namTransactions = namRows.filter(
      (row) => row.customer_name?.trim() || row.amount
    );
    const allTransactions = [...jamaTransactions, ...namTransactions];

    if (allTransactions.length === 0) {
      errors.push("لږ تر لږه یوه معامله اړینه ده");
    }

    // Validate each transaction more thoroughly
    jamaTransactions.forEach((transaction, index) => {
      if (!transaction.customer_name?.trim()) {
        errors.push(`د جمعه د ${index + 1} قطار د پیرودونکي نوم اړین دی`);
      }
      if (
        !transaction.amount ||
        isNaN(parseFloat(transaction.amount)) ||
        parseFloat(transaction.amount) <= 0
      ) {
        errors.push(`د جمعه د ${index + 1} قطار اندازه اړینه او مثبته ده`);
      }
      if (transaction.customer_name?.trim().length > 100) {
        errors.push(`د جمعه د ${index + 1} قطار د پیرودونکي نوم ډیر اوږد دی`);
      }
      if (transaction.description?.length > 200) {
        errors.push(`د جمعه د ${index + 1} قطار تفصیل ډیر اوږد دی`);
      }
    });

    namTransactions.forEach((transaction, index) => {
      if (!transaction.customer_name?.trim()) {
        errors.push(`د نام د ${index + 1} قطار د پیرودونکي نوم اړین دی`);
      }
      if (
        !transaction.amount ||
        isNaN(parseFloat(transaction.amount)) ||
        parseFloat(transaction.amount) <= 0
      ) {
        errors.push(`د نام د ${index + 1} قطار اندازه اړینه او مثبته ده`);
      }
      if (transaction.customer_name?.trim().length > 100) {
        errors.push(`د نام د ${index + 1} قطار د پیرودونکي نوم ډیر اوږد دی`);
      }
      if (transaction.description?.length > 200) {
        errors.push(`د نام د ${index + 1} قطار تفصیل ډیر اوږد دی`);
      }
    });

    return errors;
  };

  const saveDaybook = async () => {
    try {
      setLoading(true);

      // Validate before saving
      const validationErrors = validateDaybook();
      if (validationErrors.length > 0) {
        toast.show({
          type: "error",
          message: `د تصدیق ستونزې: ${validationErrors.join(", ")}`,
        });
        return;
      }

      // Check if daybook is locked
      if (currentDaybook?.status === "closed") {
        toast.show({
          type: "error",
          message: "دا ورځني کتاب تړل شوی دی او نشي تصحیح کیدای",
        });
        return;
      }

      // Prepare daybook data
      const daybookData = {
        date: currentDaybook.date,
        page_number: currentDaybook.page_number,
        notes: currentDaybook.notes || "",
        status: currentDaybook.status || "open",
      };

      // Prepare separate jama and naam transactions
      const jamaTransactions = jamaRows
        .filter((row) => row.customer_name && row.amount)
        .map((row) => ({
          customer_name: row.customer_name,
          amount: parseFloat(row.amount),
          commission: parseFloat(row.commission) || 0,
          currency: row.currency === "افغانۍ" ? "AFN" : row.currency,
          description: row.description || "",
        }));

      const naamTransactions = namRows
        .filter((row) => row.customer_name && row.amount)
        .map((row) => ({
          customer_name: row.customer_name,
          amount: parseFloat(row.amount),
          commission: parseFloat(row.commission) || 0,
          currency: row.currency === "افغانۍ" ? "AFN" : row.currency,
          description: row.description || "",
        }));

      let savedDaybook;
      if (currentDaybook.id) {
        // Update existing daybook with separate transactions
        savedDaybook = await daybooksAPI.saveWithTransactions(
          currentDaybook.id,
          daybookData,
          jamaTransactions,
          naamTransactions
        );
      } else {
        // Create new daybook first
        savedDaybook = await daybooksAPI.create(daybookData);
        setCurrentDaybook(savedDaybook);

        // Then save with separate transactions
        savedDaybook = await daybooksAPI.saveWithTransactions(
          savedDaybook.id,
          daybookData,
          jamaTransactions,
          naamTransactions
        );
      }

      toast.show({
        type: "success",
        message: "ورځني کتاب بریالیتوب سره ثبت شو",
      });

      // Update current daybook state
      setCurrentDaybook(savedDaybook);

      // Reload the daybook to get updated data
      await loadDaybook();
    } catch (error) {
      console.error("Error saving daybook:", error);

      let errorMessage = "د ورځني کتاب د ثبتولو کې ستونزه";

      if (error.response?.data?.non_field_errors) {
        // Handle unique constraint violations and other model-level errors
        const errors = error.response.data.non_field_errors;
        if (
          errors.some(
            (err) => err.includes("unique") || err.includes("already exists")
          )
        ) {
          errorMessage =
            "دا نیټه او د پاڼې شمیره دمخه شتون لري. بل شمیره وټاکئ.";
        } else {
          errorMessage += `: ${errors.join(", ")}`;
        }
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage += `: ${error.message}`;
      }

      toast.show({
        type: "error",
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Layout title='ورځني کتاب (روزنامچه)'>
        <div className='flex items-center justify-center h-64'>
          <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-sarafi-600'></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title='ورځني کتاب (روزنامچه)'>
      <div className='space-y-6'>
        {/* Navigation and Access Control Alert */}
        <div className='flex items-center justify-between'>
          <Button
            variant='outline'
            icon={ArrowLeft}
            onClick={() => navigate("/daybook-list")}
          >
            بیرته د لیست ته
          </Button>

          <div className='flex items-center space-x-3'>
            {/* Mode indicator */}
            {urlMode === "view" && (
              <div className='flex items-center space-x-2 bg-blue-50 border border-blue-200 rounded-lg px-4 py-2'>
                <Eye className='h-4 w-4 text-blue-600' />
                <span className='text-sm text-blue-700 pashto-text'>
                  د لیدلو حالت
                </span>
              </div>
            )}

            {urlMode === "edit" && (
              <div className='flex items-center space-x-2 bg-green-50 border border-green-200 rounded-lg px-4 py-2'>
                <Edit className='h-4 w-4 text-green-600' />
                <span className='text-sm text-green-700 pashto-text'>
                  د تصحیح حالت
                </span>
              </div>
            )}

            {urlMode === "new" && (
              <div className='flex items-center space-x-2 bg-purple-50 border border-purple-200 rounded-lg px-4 py-2'>
                <Plus className='h-4 w-4 text-purple-600' />
                <span className='text-sm text-purple-700 pashto-text'>
                  نوی ورځني کتاب
                </span>
              </div>
            )}

            {!canEdit && urlMode !== "new" && (
              <div className='flex items-center space-x-2 bg-yellow-50 border border-yellow-200 rounded-lg px-4 py-2'>
                <AlertCircle className='h-4 w-4 text-yellow-600' />
                <span className='text-sm text-yellow-700 pashto-text'>
                  تاسو د دې ورځني کتاب د تصحیح اجازه نلرئ
                </span>
              </div>
            )}

            {/* Mode switching buttons */}
            {currentDaybook?.id && urlMode === "view" && canEdit && (
              <Button
                variant='primary'
                icon={Edit}
                onClick={() => navigate(`/daybook/${currentDaybook.id}/edit`)}
              >
                تصحیح کول
              </Button>
            )}

            {currentDaybook?.id && urlMode === "edit" && (
              <Button
                variant='outline'
                icon={Eye}
                onClick={() => navigate(`/daybook/${currentDaybook.id}/view`)}
              >
                لیدل
              </Button>
            )}
          </div>
        </div>

        {/* د ورځني کتاب سرلیک */}
        <div className='bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-8 border border-blue-100'>
          <div className='flex items-center justify-between mb-6'>
            <div>
              <h1 className='text-3xl font-bold text-gray-800 pashto-text mb-2'>
                ورځني کتاب - صفحه {currentDaybook?.page_number}
              </h1>
              <div className='flex items-center space-x-6 mt-3'>
                <div className='flex items-center space-x-3 bg-white rounded-lg px-4 py-2 shadow-sm'>
                  <Calendar className='h-5 w-5 text-blue-600' />
                  <span className='text-gray-700 pashto-text font-medium'>
                    نیټه:
                  </span>
                  {canEdit ? (
                    <input
                      type='date'
                      value={currentDaybook?.date || ""}
                      onChange={(e) =>
                        setCurrentDaybook((prev) => ({
                          ...prev,
                          date: e.target.value,
                        }))
                      }
                      className='border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                      disabled={currentDaybook?.status === "closed"}
                    />
                  ) : (
                    <span className='text-gray-700 font-medium'>
                      {currentDaybook?.date}
                    </span>
                  )}
                </div>
                <div className='flex items-center space-x-3 bg-white rounded-lg px-4 py-2 shadow-sm'>
                  <BookOpen className='h-5 w-5 text-blue-600' />
                  <span className='text-gray-700 pashto-text font-medium'>
                    د پاڼې شمیره:
                  </span>
                  {canEdit ? (
                    <input
                      type='number'
                      value={currentDaybook?.page_number || ""}
                      onChange={(e) =>
                        setCurrentDaybook((prev) => ({
                          ...prev,
                          page_number: parseInt(e.target.value) || 1,
                        }))
                      }
                      className='border border-gray-300 rounded-md px-3 py-1 text-sm w-20 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                      min='1'
                      disabled={currentDaybook?.status === "closed"}
                    />
                  ) : (
                    <span className='text-gray-700 font-medium'>
                      {currentDaybook?.page_number}
                    </span>
                  )}
                </div>
                <div className='flex items-center space-x-3 bg-white rounded-lg px-4 py-2 shadow-sm'>
                  <User className='h-5 w-5 text-blue-600' />
                  <span className='text-gray-700 pashto-text font-medium'>
                    جوړونکی: {currentDaybook?.created_by?.full_name}
                  </span>
                </div>
                <div
                  className={`inline-flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg shadow-sm ${
                    currentDaybook?.status === "open"
                      ? "bg-green-100 text-green-800 border border-green-200"
                      : "bg-red-100 text-red-800 border border-red-200"
                  }`}
                >
                  {currentDaybook?.status === "open" ? (
                    <>
                      <Unlock className='w-4 h-4' />
                      <span className='pashto-text'>خلاص</span>
                    </>
                  ) : (
                    <>
                      <Lock className='w-4 h-4' />
                      <span className='pashto-text'>تړل شوی</span>
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className='flex items-center space-x-3'>
              <Button
                variant='outline'
                icon={Calendar}
                onClick={() => navigate("/daybook/new")}
                disabled={!canEdit}
              >
                نوی ورځني کتاب
              </Button>

              <Button
                variant='primary'
                icon={Save}
                onClick={saveDaybook}
                disabled={
                  !canEdit || loading || currentDaybook?.status === "closed"
                }
                title={
                  currentDaybook?.status === "closed"
                    ? "دا ورځني کتاب تړل شوی دی"
                    : ""
                }
              >
                {loading ? "ثبتیږي..." : "ثبت کول"}
              </Button>

              <Button
                variant={
                  currentDaybook?.status === "closed" ? "success" : "danger"
                }
                icon={currentDaybook?.status === "closed" ? Unlock : Lock}
                onClick={toggleDaybookStatus}
                disabled={!currentDaybook?.id || loading}
                title={
                  currentDaybook?.status === "closed"
                    ? "ورځني کتاب خلاص کړئ"
                    : "ورځني کتاب وتړئ"
                }
              >
                {loading
                  ? "بدلیږي..."
                  : currentDaybook?.status === "closed"
                  ? "خلاصول"
                  : "تړل"}
              </Button>
            </div>
          </div>

          {/* د ټولو لنډیز */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mt-6'>
            <div className='bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl shadow-lg border border-green-200 hover:shadow-xl transition-shadow duration-300'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-green-600 pashto-text font-medium mb-2'>
                    ټوله جمعه
                  </p>
                  <p className='text-2xl font-bold text-green-700'>
                    {currentDaybook?.total_jamah?.toLocaleString()} افغانۍ
                  </p>
                </div>
                <div className='bg-green-200 p-3 rounded-full'>
                  <ArrowUpRight className='h-8 w-8 text-green-700' />
                </div>
              </div>
            </div>

            <div className='bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-xl shadow-lg border border-red-200 hover:shadow-xl transition-shadow duration-300'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-red-600 pashto-text font-medium mb-2'>
                    ټول نام
                  </p>
                  <p className='text-2xl font-bold text-red-700'>
                    {currentDaybook?.total_naam?.toLocaleString()} افغانۍ
                  </p>
                </div>
                <div className='bg-red-200 p-3 rounded-full'>
                  <ArrowDownRight className='h-8 w-8 text-red-700' />
                </div>
              </div>
            </div>

            <div className='bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl shadow-lg border border-blue-200 hover:shadow-xl transition-shadow duration-300'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-blue-600 pashto-text font-medium mb-2'>
                    میزان
                  </p>
                  <p
                    className={`text-2xl font-bold ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  >
                    {currentDaybook?.balance >= 0 ? "+" : ""}
                    {currentDaybook?.balance?.toLocaleString()} افغانۍ
                  </p>
                </div>
                <div
                  className={`p-3 rounded-full ${
                    currentDaybook?.balance >= 0 ? "bg-green-200" : "bg-red-200"
                  }`}
                >
                  <Save
                    className={`h-8 w-8 ${
                      currentDaybook?.balance >= 0
                        ? "text-green-700"
                        : "text-red-700"
                    }`}
                  />
                </div>
              </div>
            </div>

            <div className='bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl shadow-lg border border-purple-200 hover:shadow-xl transition-shadow duration-300'>
              <div className='flex items-center justify-between'>
                <div>
                  <p className='text-sm text-purple-600 pashto-text font-medium mb-2'>
                    ټولې راکړې ورکړې
                  </p>
                  <p className='text-2xl font-bold text-purple-700'>
                    {transactions.length}
                  </p>
                </div>
                <div className='bg-purple-200 p-3 rounded-full'>
                  <Calendar className='h-8 w-8 text-purple-700' />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Two Separate Tables Side by Side */}
        <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8'>
          {/* Jama Table */}
          <div className='bg-white rounded-xl shadow-lg border border-green-200 overflow-hidden'>
            <div className='bg-gradient-to-r from-green-500 to-green-600 px-6 py-4'>
              <h3 className='text-lg font-bold text-white pashto-text text-center flex items-center justify-center'>
                <ArrowUpRight className='h-5 w-5 ml-2' />
                جمعه (اخیستل)
              </h3>
            </div>
            <div className='overflow-x-auto'>
              <table className='min-w-full text-sm'>
                <thead className='bg-gradient-to-r from-green-50 to-green-100'>
                  <tr>
                    <th className='px-4 py-3 text-center text-sm font-semibold text-green-800 w-12 border-b border-green-200'>
                      #
                    </th>
                    <th className='px-4 py-3 text-right text-sm font-semibold text-green-800 pashto-text min-w-[120px] border-b border-green-200'>
                      پیرودونکی
                    </th>
                    <th className='px-4 py-3 text-right text-sm font-semibold text-green-800 pashto-text min-w-[140px] border-b border-green-200'>
                      تفصیل
                    </th>
                    <th className='px-4 py-3 text-right text-sm font-semibold text-green-800 pashto-text min-w-[100px] border-b border-green-200'>
                      اندازه
                    </th>
                    <th className='px-4 py-3 text-right text-sm font-semibold text-green-800 pashto-text min-w-[80px] border-b border-green-200'>
                      پیسې
                    </th>
                    <th className='px-4 py-3 text-center text-sm font-semibold text-green-800 pashto-text min-w-[90px] border-b border-green-200'>
                      کمیشن
                    </th>
                    <th className='px-4 py-3 text-center text-sm font-semibold text-green-800 w-12 border-b border-green-200'>
                      ×
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-green-100'>
                  {jamaRows.map((row, index) => (
                    <tr
                      key={row.id}
                      className='hover:bg-green-50 transition-colors duration-200'
                    >
                      <td className='px-4 py-3 text-center text-sm font-medium text-green-700 border-r border-green-100'>
                        {index + 1}
                      </td>
                      <td className='px-4 py-3 border-r border-green-100'>
                        <input
                          type='text'
                          value={row.customer_name}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "customer_name",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_customer", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_customer'
                          data-sheet='جمعه'
                          className='w-full border-0 px-2 py-1 text-sm focus:ring-0 focus:outline-none pashto-text bg-transparent'
                          placeholder='نوم'
                          dir='rtl'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.description}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "description",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_description", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_description'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='تفصیل'
                          dir='rtl'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.amount}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "amount",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_amount", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_amount'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          dir='ltr'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <select
                          value={row.currency}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "currency",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_currency", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_currency'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          disabled={!canEdit}
                        >
                          <option value='افغانۍ'>افغانۍ</option>
                          <option value='ډالر'>ډالر</option>
                          <option value='یورو'>یورو</option>
                          <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                        </select>
                      </td>
                      <td className='border border-green-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.commission}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "commission",
                              e.target.value,
                              "جمعه"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "jama_commission", "جمعه")
                          }
                          data-row={index}
                          data-field='jama_commission'
                          data-sheet='جمعه'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          step='0.01'
                          dir='ltr'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-green-300 px-1 py-1 text-center'>
                        <button
                          onClick={() => deleteRow(index, "جمعه")}
                          className='p-1 text-red-600 hover:bg-red-100 rounded disabled:opacity-50 disabled:cursor-not-allowed'
                          title='قطار ړنګ کړئ'
                          disabled={!canEdit}
                        >
                          <X className='h-3 w-3' />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Nam Table */}
          <div className='bg-white rounded-lg shadow-md border-2 border-red-200'>
            <div className='bg-red-50 px-4 py-3 border-b border-red-200'>
              <h3 className='text-sm font-bold text-red-800 pashto-text text-center'>
                نام (ورکول)
              </h3>
            </div>
            <div className='overflow-x-auto'>
              <table className='min-w-full border-collapse text-xs'>
                <thead className='bg-red-50'>
                  <tr>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 w-8'>
                      #
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[100px]'>
                      پیرودونکی
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[120px]'>
                      تفصیل
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[80px]'>
                      اندازه
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-right text-xs font-medium text-red-700 pashto-text min-w-[60px]'>
                      پیسې
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 pashto-text min-w-[70px]'>
                      کمیشن
                    </th>
                    <th className='border border-red-300 px-2 py-1 text-center text-xs font-medium text-red-700 w-8'>
                      ×
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white'>
                  {namRows.map((row, index) => (
                    <tr key={row.id} className='hover:bg-red-25'>
                      <td className='border border-red-300 px-2 py-1 text-center text-xs text-red-700'>
                        {index + 1}
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.customer_name}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "customer_name",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_customer", "نام")
                          }
                          data-row={index}
                          data-field='nam_customer'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='نوم'
                          dir='rtl'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='text'
                          value={row.description}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "description",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_description", "نام")
                          }
                          data-row={index}
                          data-field='nam_description'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          placeholder='تفصیل'
                          dir='rtl'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.amount}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "amount",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_amount", "نام")
                          }
                          data-row={index}
                          data-field='nam_amount'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          dir='ltr'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <select
                          value={row.currency}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "currency",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_currency", "نام")
                          }
                          data-row={index}
                          data-field='nam_currency'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none pashto-text'
                          disabled={!canEdit}
                        >
                          <option value='افغانۍ'>افغانۍ</option>
                          <option value='ډالر'>ډالر</option>
                          <option value='یورو'>یورو</option>
                          <option value='پاکستاني روپۍ'>پاکستاني روپۍ</option>
                        </select>
                      </td>
                      <td className='border border-red-300 px-1 py-1'>
                        <input
                          type='number'
                          value={row.commission}
                          onChange={(e) =>
                            handleCellChange(
                              index,
                              "commission",
                              e.target.value,
                              "نام"
                            )
                          }
                          onKeyPress={(e) =>
                            handleKeyPress(e, index, "nam_commission", "نام")
                          }
                          data-row={index}
                          data-field='nam_commission'
                          data-sheet='نام'
                          className='w-full border-0 px-1 py-1 text-xs focus:ring-0 focus:outline-none'
                          placeholder='0'
                          step='0.01'
                          dir='ltr'
                          disabled={!canEdit}
                        />
                      </td>
                      <td className='border border-red-300 px-1 py-1 text-center'>
                        <button
                          onClick={() => deleteRow(index, "نام")}
                          className='p-1 text-red-600 hover:bg-red-100 rounded disabled:opacity-50 disabled:cursor-not-allowed'
                          title='قطار ړنګ کړئ'
                          disabled={!canEdit}
                        >
                          <X className='h-3 w-3' />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Daybook;
