from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
from django.contrib.auth.models import AbstractUser
from decimal import Decimal

# ============================================================================
# MODELS BASED ON ERD SPECIFICATION
# ============================================================================

class User(AbstractUser):
    """User model - matches USERS table in ERD"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255, default="")
    mobile_number = models.CharField(max_length=20, default="")
    full_name = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=15, blank=True, null=True)
    role = models.CharField(max_length=20, choices=[
        ('super_admin', 'Super Admin'),
        ('admin', 'Admin'),
        ('manager', 'Manager'),
        ('cashier', 'Cashier'),
        ('accountant', 'Accountant'),
    ], default='cashier')
    status = models.CharField(max_length=10, choices=[
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ], default='active')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.full_name or self.name} ({self.username})"

    class Meta:
        verbose_name_plural = "Users"
        ordering = ['-created_at']

class Company(models.Model):
    """Company model - matches COMPANIES table in ERD"""
    id = models.AutoField(primary_key=True)
    owner_name = models.CharField(max_length=100)
    company_name = models.CharField(max_length=100)
    mobile_number = models.CharField(max_length=15)
    logo = models.ImageField(upload_to='company_logos/', blank=True, null=True)
    address = models.TextField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name

    class Meta:
        verbose_name_plural = "Companies"
        ordering = ['-created_at']

class Currency(models.Model):
    """Currency model - matches CURRENCIES table in ERD"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=3, unique=True)  # AFN, USD, EUR
    symbol = models.CharField(max_length=10, blank=True, null=True)
    color_hex = models.CharField(max_length=7, default='#000000')
    exchange_rate = models.DecimalField(max_digits=10, decimal_places=4, default=1.0000)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        verbose_name_plural = "Currencies"
        ordering = ['name']

class Customer(models.Model):
    """Customer model - matches CUSTOMERS table in ERD"""
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100)
    mobile_number = models.CharField(max_length=15, blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Customers"
        ordering = ['-created_at']

class CustomerBalance(models.Model):
    """Customer Balance model - matches CUSTOMER_BALANCES table in ERD"""
    id = models.AutoField(primary_key=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='balances')
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    balance = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    last_transaction_date = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.customer.name} - {self.currency.code}: {self.balance}"

    class Meta:
        verbose_name_plural = "Customer Balances"
        unique_together = ['customer', 'currency']
        ordering = ['customer', 'currency']

class Daybook(models.Model):
    """Daybook model - matches DAYBOOKS table in ERD"""
    id = models.AutoField(primary_key=True)
    date = models.DateField()
    page_number = models.PositiveIntegerField()
    status = models.CharField(max_length=10, choices=[
        ('open', 'خلاص'),  # Open
        ('closed', 'تړل شوی'),  # Closed
    ], default='open')
    balance = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='daybooks')
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Daybook {self.page_number} - {self.date}"

    class Meta:
        verbose_name_plural = "Daybooks"
        unique_together = ['date', 'page_number']
        ordering = ['-date', '-page_number']

class JamaEntry(models.Model):
    """Jama (Credit) Entry model - matches JAMA_ENTRIES table in ERD"""
    id = models.AutoField(primary_key=True)
    daybook = models.ForeignKey(Daybook, on_delete=models.CASCADE, related_name='jama_entries')
    index = models.PositiveIntegerField()  # Entry sequence number
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='jama_entries')
    customer_name = models.CharField(max_length=100)  # Denormalized for performance
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Jama {self.index}: {self.customer_name} - {self.amount} {self.currency.code}"

    class Meta:
        verbose_name_plural = "Jama Entries (Credit)"
        unique_together = ['daybook', 'index']
        ordering = ['daybook', 'index']

class NaamEntry(models.Model):
    """Naam (Debit) Entry model - matches NAAM_ENTRIES table in ERD"""
    id = models.AutoField(primary_key=True)
    daybook = models.ForeignKey(Daybook, on_delete=models.CASCADE, related_name='naam_entries')
    index = models.PositiveIntegerField()  # Entry sequence number
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='naam_entries')
    customer_name = models.CharField(max_length=100)  # Denormalized for performance
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    description = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Naam {self.index}: {self.customer_name} - {self.amount} {self.currency.code}"

    class Meta:
        verbose_name_plural = "Naam Entries (Debit)"
        unique_together = ['daybook', 'index']
        ordering = ['daybook', 'index']

class Transaction(models.Model):
    """Transaction model - matches TRANSACTIONS table in ERD"""
    id = models.AutoField(primary_key=True)
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='transactions')
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=[
        ('jama', 'جمعه'),  # Credit
        ('naam', 'نام'),   # Debit
        ('transfer_in', 'انتقال (راتلونکی)'),
        ('transfer_out', 'انتقال (ورکونکی)'),
    ])
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    balance_before = models.DecimalField(max_digits=15, decimal_places=2)
    balance_after = models.DecimalField(max_digits=15, decimal_places=2)
    description = models.TextField(blank=True, null=True)
    daybook = models.ForeignKey(Daybook, on_delete=models.CASCADE, blank=True, null=True)
    jama_entry = models.ForeignKey(JamaEntry, on_delete=models.CASCADE, blank=True, null=True)
    naam_entry = models.ForeignKey(NaamEntry, on_delete=models.CASCADE, blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(default=timezone.now)

    def __str__(self):
        return f"{self.customer.name} - {self.transaction_type} - {self.amount} {self.currency.code}"

    class Meta:
        verbose_name_plural = "Transactions"
        ordering = ['-created_at']

# ============================================================================
# SIGNAL HANDLERS FOR AUTOMATIC BALANCE UPDATES
# ============================================================================

@receiver(post_save, sender=Transaction)
def update_customer_balance(sender, instance, created, **kwargs):
    """Update customer balance when a transaction is created"""
    if created:
        customer_balance, created_balance = CustomerBalance.objects.get_or_create(
            customer=instance.customer,
            currency=instance.currency,
            defaults={'balance': 0.00}
        )

        # Update balance based on transaction type
        if instance.transaction_type in ['jama', 'transfer_in']:
            customer_balance.balance += instance.amount
        elif instance.transaction_type in ['naam', 'transfer_out']:
            customer_balance.balance -= instance.amount

        customer_balance.last_transaction_date = instance.created_at
        customer_balance.save()

@receiver(post_save, sender=JamaEntry)
def create_jama_transaction(sender, instance, created, **kwargs):
    """Create a transaction record when a Jama entry is created"""
    if created:
        customer_balance, _ = CustomerBalance.objects.get_or_create(
            customer=instance.customer,
            currency=instance.currency,
            defaults={'balance': 0.00}
        )

        Transaction.objects.create(
            customer=instance.customer,
            currency=instance.currency,
            transaction_type='jama',
            amount=instance.amount,
            balance_before=customer_balance.balance,
            balance_after=customer_balance.balance + instance.amount,
            description=instance.description,
            daybook=instance.daybook,
            jama_entry=instance,
            created_by=instance.daybook.created_by
        )

@receiver(post_save, sender=NaamEntry)
def create_naam_transaction(sender, instance, created, **kwargs):
    """Create a transaction record when a Naam entry is created"""
    if created:
        customer_balance, _ = CustomerBalance.objects.get_or_create(
            customer=instance.customer,
            currency=instance.currency,
            defaults={'balance': 0.00}
        )

        Transaction.objects.create(
            customer=instance.customer,
            currency=instance.currency,
            transaction_type='naam',
            amount=instance.amount,
            balance_before=customer_balance.balance,
            balance_after=customer_balance.balance - instance.amount,
            description=instance.description,
            daybook=instance.daybook,
            naam_entry=instance,
            created_by=instance.daybook.created_by
        )
